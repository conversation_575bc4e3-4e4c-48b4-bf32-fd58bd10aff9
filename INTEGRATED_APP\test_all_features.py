#!/usr/bin/env python3
"""
Dr. Resume - Comprehensive Feature Testing Script
================================================

This script tests all US-01 to US-10 features with demo data to ensure
the application is fully functional.

Usage: python test_all_features.py
"""

import requests
import json
import time
import os
from pathlib import Path

# Configuration
API_BASE_URL = 'http://localhost:5000/api'
BASE_URL = 'http://localhost:5000'

# Test data
TEST_USER = {
    'email': '<EMAIL>',
    'password': 'DemoUser123!',
    'first_name': 'Demo',
    'last_name': 'User'
}

TEST_JOB = {
    'title': 'Senior Software Engineer',
    'company': 'Tech Corp',
    'location': 'San Francisco, CA',
    'job_type': 'full-time',
    'description': '''We are seeking a Senior Software Engineer to join our dynamic team. 

Key Responsibilities:
- Design and develop scalable web applications using Python, JavaScript, and React
- Collaborate with cross-functional teams to deliver high-quality software solutions
- Implement best practices for code quality, testing, and deployment
- Mentor junior developers and contribute to technical decision-making
- Work with cloud platforms like AWS, Docker, and Kubernetes

Required Skills:
- 5+ years of experience in software development
- Proficiency in Python, JavaScript, React, Node.js
- Experience with databases (PostgreSQL, MongoDB)
- Knowledge of cloud platforms (AWS, Azure, GCP)
- Strong understanding of software engineering principles
- Experience with agile development methodologies
- Excellent communication and problem-solving skills

Preferred Qualifications:
- Bachelor's degree in Computer Science or related field
- Experience with machine learning and AI technologies
- Knowledge of DevOps practices and CI/CD pipelines
- Contributions to open-source projects'''
}

# Global variables
auth_token = None
user_id = None
resume_id = None
job_id = None

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"🧪 {title}")
    print("="*60)

def print_result(test_name, success, message=""):
    """Print test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if message:
        print(f"   {message}")

def test_health_check():
    """Test application health"""
    print_header("Health Check")
    
    try:
        response = requests.get(f'{BASE_URL}/health', timeout=10)
        data = response.json()
        
        if response.status_code == 200:
            print_result("Health Check", True, f"App status: {data.get('status', {}).get('app', 'unknown')}")
            return True
        else:
            print_result("Health Check", False, f"Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print_result("Health Check", False, f"Error: {str(e)}")
        return False

def test_us01_registration():
    """Test US-01: User Registration"""
    print_header("US-01: User Registration")
    
    try:
        response = requests.post(
            f'{API_BASE_URL}/register',
            json=TEST_USER,
            timeout=10
        )
        
        data = response.json()
        
        if response.status_code == 201 and data.get('success'):
            global user_id
            user_id = data.get('user', {}).get('id')
            print_result("User Registration", True, f"User ID: {user_id}")
            return True
        elif response.status_code == 400 and ('already' in data.get('message', '').lower() or 'exists' in data.get('message', '').lower()):
            print_result("User Registration", True, "User already exists (expected for repeated tests)")
            return True
        else:
            print_result("User Registration", False, f"Error: {data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_result("User Registration", False, f"Error: {str(e)}")
        return False

def test_us02_login():
    """Test US-02: Login + JWT"""
    print_header("US-02: Login + JWT Authentication")
    
    try:
        response = requests.post(
            f'{API_BASE_URL}/login',
            json={
                'email': TEST_USER['email'],
                'password': TEST_USER['password']
            },
            timeout=10
        )
        
        data = response.json()
        
        if response.status_code == 200 and data.get('success'):
            global auth_token, user_id
            # Try both possible token locations
            auth_token = data.get('access_token') or data.get('tokens', {}).get('access_token')
            user_id = data.get('user', {}).get('id')

            if auth_token:
                print_result("User Login", True, f"JWT Token received (length: {len(auth_token)})")
                return True
            else:
                print_result("User Login", False, "No access token in response")
                return False
        else:
            print_result("User Login", False, f"Error: {data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_result("User Login", False, f"Error: {str(e)}")
        return False

def test_us04_job_description():
    """Test US-04: Job Description Upload"""
    print_header("US-04: Job Description Management")
    
    if not auth_token:
        print_result("Job Description", False, "No auth token available")
        return False
    
    try:
        headers = {'Authorization': f'Bearer {auth_token}'}
        # Prepare job data in the expected format
        job_data = {
            'title': TEST_JOB['title'],
            'description': TEST_JOB['description'],
            'company': TEST_JOB.get('company'),
            'location': TEST_JOB.get('location'),
            'job_type': TEST_JOB.get('job_type')
        }

        response = requests.post(
            f'{API_BASE_URL}/jobs',
            json=job_data,
            headers=headers,
            timeout=10
        )
        
        data = response.json()
        
        if response.status_code == 201 and data.get('success'):
            global job_id
            job_id = data.get('job', {}).get('id')
            print_result("Job Description Upload", True, f"Job ID: {job_id}")
            return True
        else:
            print_result("Job Description Upload", False, f"Error: {data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_result("Job Description Upload", False, f"Error: {str(e)}")
        return False

def test_us03_resume_upload():
    """Test US-03: Resume Upload (create a sample resume)"""
    print_header("US-03: Resume Upload")
    
    if not auth_token:
        print_result("Resume Upload", False, "No auth token available")
        return False
    
    # Create a sample resume file
    sample_resume_content = """
DEMO USER
Software Engineer

CONTACT INFORMATION
Email: <EMAIL>
Phone: (*************
Location: San Francisco, CA

PROFESSIONAL SUMMARY
Experienced Software Engineer with 5+ years of expertise in full-stack development.
Proficient in Python, JavaScript, React, and cloud technologies. Strong background
in building scalable web applications and leading development teams.

TECHNICAL SKILLS
Programming Languages: Python, JavaScript, TypeScript, Java
Web Technologies: React, Node.js, HTML5, CSS3, REST APIs
Databases: PostgreSQL, MongoDB, Redis
Cloud Platforms: AWS, Docker, Kubernetes
Tools: Git, Jenkins, JIRA, Agile methodologies

PROFESSIONAL EXPERIENCE

Senior Software Engineer | Tech Solutions Inc. | 2020 - Present
• Developed and maintained scalable web applications using Python and React
• Led a team of 4 developers in implementing new features and optimizations
• Implemented CI/CD pipelines reducing deployment time by 60%
• Collaborated with product managers to define technical requirements

Software Engineer | StartupCorp | 2018 - 2020
• Built RESTful APIs and microservices using Python and Flask
• Developed responsive front-end interfaces using React and JavaScript
• Optimized database queries improving application performance by 40%
• Participated in code reviews and mentored junior developers

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley | 2018

CERTIFICATIONS
• AWS Certified Solutions Architect
• Certified Kubernetes Administrator (CKA)
"""
    
    try:
        # Create temporary resume file (simple text file with PDF extension for testing)
        resume_path = Path('temp_resume.pdf')
        with open(resume_path, 'w') as f:
            f.write(sample_resume_content)

        headers = {'Authorization': f'Bearer {auth_token}'}
        files = {'file': ('demo_resume.pdf', open(resume_path, 'rb'), 'application/pdf')}
        data = {'title': 'Demo Software Engineer Resume'}
        
        response = requests.post(
            f'{API_BASE_URL}/resumes/upload',
            files=files,
            data=data,
            headers=headers,
            timeout=30
        )
        
        # Clean up
        files['file'][1].close()
        resume_path.unlink()
        
        response_data = response.json()
        
        if response.status_code == 201 and response_data.get('success'):
            global resume_id
            resume_id = response_data.get('resume', {}).get('id')
            print_result("Resume Upload", True, f"Resume ID: {resume_id}")
            return True
        else:
            print_result("Resume Upload", False, f"Error: {response_data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_result("Resume Upload", False, f"Error: {str(e)}")
        return False

def test_us05_keyword_parsing():
    """Test US-05: Keyword Parsing"""
    print_header("US-05: Keyword Parsing")
    
    if not auth_token:
        print_result("Keyword Parsing", False, "No auth token available")
        return False
    
    try:
        headers = {'Authorization': f'Bearer {auth_token}'}
        
        # Test keyword extraction
        response = requests.post(
            f'{API_BASE_URL}/keywords/extract',
            json={'text': TEST_JOB['description']},
            headers=headers,
            timeout=15
        )
        
        data = response.json()
        
        if response.status_code == 200 and data.get('success'):
            keywords = data.get('keywords', [])
            print_result("Keyword Extraction", True, f"Extracted {len(keywords)} keywords")
            if keywords:
                # Extract keyword names from the keyword objects
                keyword_names = [k.get('keyword', str(k)) if isinstance(k, dict) else str(k) for k in keywords[:5]]
                print(f"   Sample keywords: {', '.join(keyword_names)}")
            return True
        else:
            print_result("Keyword Extraction", False, f"Error: {data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print_result("Keyword Extraction", False, f"Error: {str(e)}")
        return False

def test_us06_matching_score():
    """Test US-06: Matching Score Calculation"""
    print_header("US-06: Matching Score Calculation")

    if not auth_token or not resume_id or not job_id:
        print_result("Matching Score", False, "Missing prerequisites (auth, resume, or job)")
        return False

    try:
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Test matching calculation
        response = requests.post(
            f'{API_BASE_URL}/matching/calculate',
            json={
                'resume_id': resume_id,
                'job_description_id': job_id
            },
            headers=headers,
            timeout=15
        )

        data = response.json()

        if response.status_code == 200 and data.get('success'):
            score = data.get('match_score', 0)
            print_result("Matching Score Calculation", True, f"Match score: {score}%")
            return True
        else:
            print_result("Matching Score Calculation", False, f"Error: {data.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print_result("Matching Score Calculation", False, f"Error: {str(e)}")
        return False

def test_us07_suggestions():
    """Test US-07: AI Suggestions"""
    print_header("US-07: AI Suggestions (Basic)")

    if not auth_token or not resume_id or not job_id:
        print_result("AI Suggestions", False, "Missing prerequisites")
        return False

    try:
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Test suggestion generation
        response = requests.post(
            f'{API_BASE_URL}/suggestions/generate',
            json={
                'resume_id': resume_id,
                'job_description_id': job_id
            },
            headers=headers,
            timeout=20
        )

        data = response.json()

        if response.status_code == 200 and data.get('success'):
            suggestions = data.get('suggestions', [])
            print_result("AI Suggestions", True, f"Generated {len(suggestions)} suggestions")
            return True
        else:
            print_result("AI Suggestions", False, f"Error: {data.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print_result("AI Suggestions", False, f"Error: {str(e)}")
        return False

def test_us07_premium_suggestions():
    """Test US-07.1: Premium OpenAI Suggestions"""
    print_header("US-07.1: Premium OpenAI Suggestions")

    if not auth_token or not resume_id or not job_id:
        print_result("Premium Suggestions", False, "Missing prerequisites")
        return False

    try:
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Test premium suggestion generation
        response = requests.post(
            f'{API_BASE_URL}/premium_suggestions',
            json={
                'resume_id': resume_id,
                'job_id': job_id
            },
            headers=headers,
            timeout=30
        )

        data = response.json()

        if response.status_code == 200 and data.get('success'):
            suggestions = data.get('suggestions', [])
            print_result("Premium OpenAI Suggestions", True, f"Generated {len(suggestions)} premium suggestions")
            return True
        elif response.status_code == 403:
            print_result("Premium Suggestions", True, "Premium access required (expected for free users)")
            return True
        else:
            print_result("Premium Suggestions", False, f"Error: {data.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print_result("Premium Suggestions", False, f"Error: {str(e)}")
        return False

def test_us08_dashboard():
    """Test US-08: Dashboard and History"""
    print_header("US-08: Dashboard and Scan History")

    if not auth_token:
        print_result("Dashboard", False, "No auth token available")
        return False

    try:
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Test dashboard stats
        response = requests.get(
            f'{API_BASE_URL}/dashboard/stats',
            headers=headers,
            timeout=10
        )

        data = response.json()

        if response.status_code == 200 and data.get('success'):
            stats = data.get('stats', {})
            print_result("Dashboard Stats", True, f"Total scans: {stats.get('total_scans', 0)}")

            # Test scan history
            response = requests.get(
                f'{API_BASE_URL}/history',
                headers=headers,
                timeout=10
            )

            history_data = response.json()
            if response.status_code == 200:
                scans = history_data.get('scans', [])
                print_result("Scan History", True, f"Found {len(scans)} historical scans")
                return True
            else:
                print_result("Scan History", False, f"Error: {history_data.get('message', 'Unknown error')}")
                return False
        else:
            print_result("Dashboard Stats", False, f"Error: {data.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print_result("Dashboard", False, f"Error: {str(e)}")
        return False

def test_us09_api_protection():
    """Test US-09: API Protection and JWT Security"""
    print_header("US-09: API Protection and JWT Security")

    try:
        # Test protected endpoint without token
        response = requests.get(f'{API_BASE_URL}/me', timeout=10)

        if response.status_code == 401:
            print_result("JWT Protection (No Token)", True, "Correctly rejected unauthorized request")
        else:
            print_result("JWT Protection (No Token)", False, f"Expected 401, got {response.status_code}")
            return False

        # Test with valid token
        if auth_token:
            headers = {'Authorization': f'Bearer {auth_token}'}
            response = requests.get(f'{API_BASE_URL}/me', headers=headers, timeout=10)

            if response.status_code == 200:
                print_result("JWT Protection (Valid Token)", True, "Correctly accepted authorized request")
                return True
            else:
                print_result("JWT Protection (Valid Token)", False, f"Expected 200, got {response.status_code}")
                return False
        else:
            print_result("JWT Protection", False, "No auth token to test with")
            return False

    except Exception as e:
        print_result("API Protection", False, f"Error: {str(e)}")
        return False

def test_us10_account_settings():
    """Test US-10: Account Settings Management"""
    print_header("US-10: Account Settings Management")

    if not auth_token:
        print_result("Account Settings", False, "No auth token available")
        return False

    try:
        headers = {'Authorization': f'Bearer {auth_token}'}

        # Test get profile
        response = requests.get(
            f'{API_BASE_URL}/account/profile',
            headers=headers,
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            profile = data.get('profile', {})
            print_result("Get Profile", True, f"Profile for: {profile.get('email', 'Unknown')}")

            # Test update profile
            update_data = {
                'first_name': 'Updated Demo',
                'last_name': 'User Updated'
            }

            response = requests.put(
                f'{API_BASE_URL}/account/profile',
                json=update_data,
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                print_result("Update Profile", True, "Profile updated successfully")
                return True
            else:
                update_result = response.json()
                print_result("Update Profile", False, f"Error: {update_result.get('message', 'Unknown error')}")
                return False
        else:
            data = response.json()
            print_result("Get Profile", False, f"Error: {data.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print_result("Account Settings", False, f"Error: {str(e)}")
        return False

def run_all_tests():
    """Run all feature tests"""
    print_header("Dr. Resume - Comprehensive Feature Testing (US-01 to US-10)")
    print("Testing all user stories with demo data...")

    results = []

    # Test each feature in order
    results.append(("Health Check", test_health_check()))
    results.append(("US-01 Registration", test_us01_registration()))
    results.append(("US-02 Login + JWT", test_us02_login()))
    results.append(("US-04 Job Description", test_us04_job_description()))
    results.append(("US-03 Resume Upload", test_us03_resume_upload()))
    results.append(("US-05 Keyword Parsing", test_us05_keyword_parsing()))
    results.append(("US-06 Matching Score", test_us06_matching_score()))
    results.append(("US-07 AI Suggestions", test_us07_suggestions()))
    results.append(("US-07.1 Premium Suggestions", test_us07_premium_suggestions()))
    results.append(("US-08 Dashboard", test_us08_dashboard()))
    results.append(("US-09 API Protection", test_us09_api_protection()))
    results.append(("US-10 Account Settings", test_us10_account_settings()))

    # Print summary
    print_header("Test Summary - All US Features")
    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        print_result(test_name, result)

    print(f"\n📊 Results: {passed}/{total} tests passed")
    print(f"📈 Success Rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("🎉 ALL US-01 TO US-10 FEATURES ARE FULLY FUNCTIONAL!")
        print("✅ The Dr. Resume application is ready for production use.")
    else:
        print("⚠️  Some features need attention. Check the detailed results above.")
        print("🔧 Focus on fixing the failed tests to achieve full functionality.")

    return passed == total

if __name__ == '__main__':
    success = run_all_tests()
    exit(0 if success else 1)
