#!/usr/bin/env python3
"""
Cleanup Dr. Resume Project - Remove unnecessary files
"""

import os
import shutil
import glob

def cleanup_cache_files():
    """Remove all cache and temporary files"""
    print("🧹 Starting cleanup of cache and temporary files...")
    
    # Define patterns to remove
    patterns_to_remove = [
        "**/__pycache__",
        "**/.pytest_cache", 
        "**/*.pyc",
        "**/*.pyo",
        "**/*.pyd",
        "**/temp_*",
        "**/.DS_Store",
        "**/Thumbs.db",
        "**/*.log",
        "**/.coverage",
        "**/htmlcov",
        "**/.tox",
        "**/.cache",
        "**/node_modules",
        "**/.vscode/settings.json",
        "**/.idea"
    ]
    
    removed_count = 0
    
    # Walk through all directories
    for root, dirs, files in os.walk("."):
        # Remove __pycache__ directories
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            try:
                shutil.rmtree(pycache_path)
                print(f"   ✅ Removed: {pycache_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Failed to remove {pycache_path}: {e}")
        
        # Remove .pytest_cache directories
        if ".pytest_cache" in dirs:
            pytest_path = os.path.join(root, ".pytest_cache")
            try:
                shutil.rmtree(pytest_path)
                print(f"   ✅ Removed: {pytest_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Failed to remove {pytest_path}: {e}")
        
        # Remove .pyc files
        for file in files:
            if file.endswith(('.pyc', '.pyo', '.pyd')):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    print(f"   ✅ Removed: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"   ❌ Failed to remove {file_path}: {e}")
    
    print(f"\n🎉 Cleanup completed! Removed {removed_count} cache/temporary items.")

def list_project_structure():
    """List the current project structure"""
    print("\n📁 Current Project Structure:")
    
    us_folders = []
    other_items = []
    
    for item in os.listdir("."):
        if os.path.isdir(item):
            if item.startswith("US-"):
                us_folders.append(item)
            else:
                other_items.append(item)
    
    # Sort US folders
    us_folders.sort()
    
    print("\n📂 US Folders (Individual Features):")
    for folder in us_folders:
        print(f"   ├── {folder}/")
        # Show subfolders
        try:
            subfolders = [f for f in os.listdir(folder) if os.path.isdir(os.path.join(folder, f))]
            for subfolder in sorted(subfolders):
                print(f"   │   ├── {subfolder}/")
        except:
            pass
    
    print("\n📂 Other Directories:")
    for item in sorted(other_items):
        if os.path.isdir(item):
            print(f"   ├── {item}/")
    
    print("\n📄 Root Files:")
    for item in os.listdir("."):
        if os.path.isfile(item):
            print(f"   ├── {item}")

if __name__ == "__main__":
    print("🏥 Dr. Resume Project Cleanup")
    print("=" * 50)
    
    # Show current structure
    list_project_structure()
    
    # Perform cleanup
    cleanup_cache_files()
    
    print("\n✅ Cleanup completed successfully!")
    print("📋 Next: Integration of all US features into INTEGRATED_APP")
