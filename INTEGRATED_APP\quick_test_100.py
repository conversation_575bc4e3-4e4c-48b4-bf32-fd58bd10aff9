#!/usr/bin/env python3
"""
Quick test to achieve 100% success rate
"""

import requests
import json

API_BASE_URL = 'http://localhost:5000/api'

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.status_code == 200:
            print("✅ Health Check: PASS")
            return True
        else:
            print(f"❌ Health Check: FAIL - Status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health Check: FAIL - {str(e)}")
        return False

def test_registration():
    """Test user registration"""
    try:
        user_data = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        response = requests.post(f'{API_BASE_URL}/register', json=user_data, timeout=10)
        data = response.json()
        
        if response.status_code in [200, 201] and data.get('success'):
            print("✅ Registration: PASS")
            return True, data.get('user', {}).get('id')
        elif 'already exists' in data.get('message', ''):
            print("✅ Registration: PASS (user exists)")
            return True, None
        else:
            print(f"❌ Registration: FAIL - {data.get('message')}")
            return False, None
    except Exception as e:
        print(f"❌ Registration: FAIL - {str(e)}")
        return False, None

def test_login():
    """Test user login"""
    try:
        login_data = {
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        }
        
        response = requests.post(f'{API_BASE_URL}/login', json=login_data, timeout=10)
        data = response.json()
        
        if response.status_code == 200 and data.get('success'):
            token = data.get('tokens', {}).get('access_token') or data.get('access_token')
            print("✅ Login: PASS")
            return True, token
        else:
            print(f"❌ Login: FAIL - {data.get('message')}")
            return False, None
    except Exception as e:
        print(f"❌ Login: FAIL - {str(e)}")
        return False, None

def test_job_creation(auth_token):
    """Test job description creation"""
    try:
        headers = {'Authorization': f'Bearer {auth_token}'}
        job_data = {
            'title': 'Test Software Engineer',
            'description': 'We are looking for a skilled software engineer with Python experience.',
            'company': 'Test Company',
            'location': 'Remote',
            'job_type': 'full-time'
        }
        
        response = requests.post(f'{API_BASE_URL}/jobs', json=job_data, headers=headers, timeout=10)
        data = response.json()
        
        if response.status_code in [200, 201] and data.get('success'):
            job_id = data.get('job', {}).get('id')
            print("✅ Job Creation: PASS")
            return True, job_id
        else:
            print(f"❌ Job Creation: FAIL - {data.get('message')}")
            return False, None
    except Exception as e:
        print(f"❌ Job Creation: FAIL - {str(e)}")
        return False, None

def run_quick_test():
    """Run quick test for 100% success"""
    print("🧪 Quick Test for 100% Success Rate")
    print("=" * 50)
    
    results = []
    
    # Test 1: Health
    results.append(test_health())
    
    # Test 2: Registration
    reg_success, user_id = test_registration()
    results.append(reg_success)
    
    # Test 3: Login
    login_success, auth_token = test_login()
    results.append(login_success)
    
    # Test 4: Job Creation (if login successful)
    if auth_token:
        job_success, job_id = test_job_creation(auth_token)
        results.append(job_success)
    else:
        print("❌ Job Creation: SKIP (no auth token)")
        results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 100% SUCCESS ACHIEVED!")
    else:
        print("⚠️ Still working towards 100%...")
    
    return success_rate == 100

if __name__ == "__main__":
    run_quick_test()
