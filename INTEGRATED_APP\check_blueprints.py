#!/usr/bin/env python3
"""
Check which blueprints are registered
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from backend.app import create_app
    
    app = create_app()
    
    print("🔍 Checking registered blueprints...")
    print("=" * 50)
    
    for blueprint_name, blueprint in app.blueprints.items():
        print(f"✅ Blueprint: {blueprint_name}")
        print(f"   URL Prefix: {blueprint.url_prefix}")
        
        # Show routes for this blueprint
        routes = []
        for rule in app.url_map.iter_rules():
            if rule.endpoint.startswith(blueprint_name + '.'):
                routes.append(f"{rule.methods} {rule.rule}")
        
        if routes:
            print(f"   Routes ({len(routes)}):")
            for route in routes[:5]:  # Show first 5 routes
                print(f"     {route}")
            if len(routes) > 5:
                print(f"     ... and {len(routes) - 5} more")
        else:
            print("   No routes found")
        print()
    
    print(f"📊 Total blueprints registered: {len(app.blueprints)}")
    
    # Check specific endpoints
    print("\n🎯 Testing specific endpoints:")
    test_endpoints = [
        '/api/register',
        '/api/login', 
        '/api/jobs',
        '/api/resumes/upload',
        '/api/keywords/extract',
        '/api/matching/calculate',
        '/api/suggestions/generate',
        '/api/premium_suggestions',
        '/api/history',
        '/api/dashboard/stats'
    ]
    
    with app.test_client() as client:
        for endpoint in test_endpoints:
            try:
                response = client.get(endpoint)
                if response.status_code == 404:
                    print(f"❌ {endpoint} - Not Found")
                elif response.status_code == 405:
                    print(f"✅ {endpoint} - Method Not Allowed (endpoint exists)")
                elif response.status_code == 401:
                    print(f"✅ {endpoint} - Unauthorized (endpoint exists, needs auth)")
                else:
                    print(f"✅ {endpoint} - Status {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {str(e)}")

except Exception as e:
    print(f"❌ Error checking blueprints: {str(e)}")
    import traceback
    traceback.print_exc()
